/* Admin Panel Styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
    background: #f5f7fa;
    direction: rtl;
}

/* Admin Header */
.admin-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.admin-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.admin-logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.admin-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Admin Sidebar */
.admin-sidebar {
    position: fixed;
    top: 70px;
    right: 0;
    width: 250px;
    height: calc(100vh - 70px);
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.admin-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-menu li {
    border-bottom: 1px solid #eee;
}

.admin-menu a {
    display: block;
    padding: 1rem 1.5rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.admin-menu a:hover,
.admin-menu a.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

/* Main Content */
.admin-main {
    margin-top: 70px;
    margin-right: 250px;
    padding: 2rem;
    min-height: calc(100vh - 70px);
}

.admin-section {
    display: none;
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.admin-section.active {
    display: block;
}

.admin-section h2 {
    color: #667eea;
    margin-bottom: 2rem;
    font-size: 2rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.stat-card h3 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    opacity: 0.9;
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    margin: 0;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.admin-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    margin-left: 0.5rem;
}

.admin-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.admin-btn.secondary {
    background: #6c757d;
}

.admin-btn.danger {
    background: #dc3545;
}

/* Tables */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #ddd;
}

.admin-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.admin-table tr:hover {
    background: #f8f9fa;
}

/* Modal */
.form-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #667eea;
    border-radius: 10px;
    padding: 3rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.upload-area:hover {
    background: rgba(102, 126, 234, 0.1);
}

.upload-text {
    color: #667eea;
    font-size: 1.2rem;
    font-weight: 500;
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-item-actions {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    gap: 0.5rem;
}

.gallery-item-actions button {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* Messages */
.admin-message {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 1rem 2rem;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    z-index: 3000;
    transition: all 0.3s ease;
}

.admin-message.success {
    background: #28a745;
}

.admin-message.error {
    background: #dc3545;
}

/* Loading */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 4000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Branch Cards */
.branch-card-admin {
    background: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.branch-card-admin h4 {
    color: #667eea;
    margin-bottom: 0.5rem;
}

.branch-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

/* Message Cards */
.message-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
}

.message-header h4 {
    color: #667eea;
    margin: 0;
}

.message-date {
    color: #666;
    font-size: 0.9rem;
}

.message-content p {
    margin: 0.5rem 0;
    line-height: 1.6;
}

.message-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

/* Activity List */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background 0.3s ease;
}

.activity-item:hover {
    background: #f8f9fa;
}

.activity-text {
    font-weight: 500;
    color: #333;
}

.activity-time {
    color: #666;
    font-size: 0.9rem;
}

/* No Data Message */
.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 1rem 0;
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

/* Users Table Container */
.users-table-container {
    overflow-x: auto;
    margin-top: 1rem;
}

/* Settings Form */
.settings-form {
    max-width: 600px;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-left: 0.5rem;
}

/* Content Form */
.content-form {
    max-width: 800px;
    margin-bottom: 2rem;
}

.content-form h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
}

/* Upload Section */
.upload-section {
    margin-bottom: 2rem;
}

.upload-section h3 {
    color: #667eea;
    margin-bottom: 1rem;
}

/* Recent Activity */
.recent-activity {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.recent-activity h3 {
    color: #667eea;
    margin-bottom: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .admin-sidebar.mobile-open {
        transform: translateX(0);
    }

    .admin-main {
        margin-right: 0;
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .admin-nav {
        padding: 1rem;
    }

    .admin-logo {
        font-size: 1.2rem;
    }

    .modal-content {
        width: 95%;
        padding: 1.5rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .admin-btn {
        margin: 0.25rem 0;
    }

    .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .branch-actions,
    .message-actions {
        flex-direction: column;
    }

    .admin-table {
        font-size: 0.9rem;
    }

    .admin-table th,
    .admin-table td {
        padding: 0.5rem;
    }
}

/* Enhanced Messages Section */
.messages-list {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e0e0e0;
}

.messages-list h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.messages-list h3::before {
    content: "📧";
    font-size: 1.2rem;
}

/* Contact Section in Messages */
.content-form {
    background: #f8f9ff;
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.content-form h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.content-form h3::before {
    content: "⚙️";
    font-size: 1.2rem;
}
