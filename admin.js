// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
    authDomain: "al-salamat.firebaseapp.com",
    databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
    projectId: "al-salamat",
    storageBucket: "al-salamat.firebasestorage.app",
    messagingSenderId: "108512109295",
    appId: "1:108512109295:web:84f99d95019e2101dcb11a"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const database = firebase.database();
const storage = firebase.storage();

// Global variables
let currentUser = null;
let branches = [];
let users = [];

// Check authentication on page load
document.addEventListener('DOMContentLoaded', function() {
    checkAdminAuth();
    loadDashboardData();
    setupEventListeners();
});

// Authentication check
function checkAdminAuth() {
    const user = localStorage.getItem('user');
    if (!user) {
        window.location.href = 'login.html';
        return;
    }
    
    currentUser = JSON.parse(user);
    document.getElementById('admin-username').textContent = `مرحباً، ${currentUser.displayName || 'المدير'}`;
    
    // Check if user has admin privileges (you can implement this based on your needs)
    checkAdminPrivileges();
}

// Check admin privileges
async function checkAdminPrivileges() {
    try {
        const userRef = database.ref(`users/${currentUser.displayName?.replace(/\s+/g, '_')}`);
        const snapshot = await userRef.once('value');
        const userData = snapshot.val();
        
        if (!userData || userData.role !== 'admin') {
            showMessage('ليس لديك صلاحيات للوصول لهذه الصفحة', 'error');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }
    } catch (error) {
        console.error('Error checking admin privileges:', error);
    }
}

// Setup event listeners
function setupEventListeners() {
    // Company info form
    document.getElementById('company-info-form').addEventListener('submit', handleCompanyInfoUpdate);
    
    // Branch form
    document.getElementById('branch-form').addEventListener('submit', handleAddBranch);
    
    // Site settings form
    document.getElementById('site-settings-form').addEventListener('submit', handleSiteSettings);
}

// Navigation functions
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from all menu items
    document.querySelectorAll('.admin-menu a').forEach(link => {
        link.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.add('active');
    
    // Add active class to clicked menu item
    event.target.classList.add('active');
    
    // Load section-specific data
    loadSectionData(sectionId);
}

// Load section-specific data
function loadSectionData(sectionId) {
    switch(sectionId) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'branches':
            loadBranches();
            break;
        case 'users':
            loadUsers();
            break;
        case 'messages':
            loadMessages();
            break;
        case 'gallery':
            loadGallery();
            break;
    }
}

// Dashboard functions
async function loadDashboardData() {
    try {
        showLoading(true);
        
        // Load users count
        const usersSnapshot = await database.ref('users').once('value');
        const usersData = usersSnapshot.val();
        const usersCount = usersData ? Object.keys(usersData).length : 0;
        document.getElementById('total-users').textContent = usersCount;
        
        // Load messages count
        const messagesSnapshot = await database.ref('contactForms').once('value');
        const messagesData = messagesSnapshot.val();
        const messagesCount = messagesData ? Object.keys(messagesData).length : 0;
        document.getElementById('new-messages').textContent = messagesCount;
        
        // Load recent activity
        await loadRecentActivity();
        
        showLoading(false);
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showMessage('خطأ في تحميل بيانات لوحة التحكم', 'error');
        showLoading(false);
    }
}

// Load recent activity
async function loadRecentActivity() {
    try {
        const activityRef = database.ref('loginActivity').orderByChild('loginTime').limitToLast(10);
        const snapshot = await activityRef.once('value');
        const activities = snapshot.val();
        
        const activityList = document.getElementById('recent-activity-list');
        activityList.innerHTML = '';
        
        if (activities) {
            Object.values(activities).reverse().forEach(activity => {
                const activityItem = document.createElement('div');
                activityItem.className = 'activity-item';
                activityItem.innerHTML = `
                    <div class="activity-text">تسجيل دخول: ${activity.email}</div>
                    <div class="activity-time">${formatDate(activity.loginTime)}</div>
                `;
                activityList.appendChild(activityItem);
            });
        } else {
            activityList.innerHTML = '<div class="no-data">لا توجد أنشطة حديثة</div>';
        }
    } catch (error) {
        console.error('Error loading recent activity:', error);
    }
}

// Company info update
async function handleCompanyInfoUpdate(event) {
    event.preventDefault();
    
    const title = document.getElementById('company-title').value;
    const subtitle = document.getElementById('company-subtitle').value;
    const description = document.getElementById('company-description').value;
    
    try {
        showLoading(true);
        
        await database.ref('siteContent').set({
            title: title,
            subtitle: subtitle,
            description: description,
            updatedAt: new Date().toISOString(),
            updatedBy: currentUser.uid
        });
        
        showMessage('تم حفظ معلومات الشركة بنجاح', 'success');
        showLoading(false);
    } catch (error) {
        console.error('Error updating company info:', error);
        showMessage('خطأ في حفظ معلومات الشركة', 'error');
        showLoading(false);
    }
}

// Branches management
async function loadBranches() {
    try {
        const branchesRef = database.ref('branches');
        const snapshot = await branchesRef.once('value');
        const branchesData = snapshot.val();
        
        const branchesList = document.getElementById('branches-list');
        branchesList.innerHTML = '';
        
        if (branchesData) {
            branches = Object.entries(branchesData).map(([key, value]) => ({id: key, ...value}));
            
            branches.forEach(branch => {
                const branchCard = document.createElement('div');
                branchCard.className = 'branch-card-admin';
                branchCard.innerHTML = `
                    <h4>${branch.name}</h4>
                    <p>${branch.address}</p>
                    <p>${branch.phone}</p>
                    <div class="branch-actions">
                        <button onclick="editBranch('${branch.id}')" class="admin-btn">تعديل</button>
                        <button onclick="deleteBranch('${branch.id}')" class="admin-btn danger">حذف</button>
                    </div>
                `;
                branchesList.appendChild(branchCard);
            });
        } else {
            branchesList.innerHTML = '<div class="no-data">لا توجد فروع مضافة</div>';
        }
    } catch (error) {
        console.error('Error loading branches:', error);
        showMessage('خطأ في تحميل الفروع', 'error');
    }
}

// Add branch
async function handleAddBranch(event) {
    event.preventDefault();
    
    const name = document.getElementById('branch-name').value;
    const address = document.getElementById('branch-address').value;
    const phone = document.getElementById('branch-phone').value;
    
    try {
        showLoading(true);
        
        const branchRef = database.ref('branches').push();
        await branchRef.set({
            name: name,
            address: address,
            phone: phone,
            createdAt: new Date().toISOString(),
            createdBy: currentUser.uid
        });
        
        showMessage('تم إضافة الفرع بنجاح', 'success');
        hideAddBranchForm();
        loadBranches();
        showLoading(false);
    } catch (error) {
        console.error('Error adding branch:', error);
        showMessage('خطأ في إضافة الفرع', 'error');
        showLoading(false);
    }
}

// Show/hide branch form
function showAddBranchForm() {
    document.getElementById('add-branch-form').style.display = 'flex';
}

function hideAddBranchForm() {
    document.getElementById('add-branch-form').style.display = 'none';
    document.getElementById('branch-form').reset();
}

// Delete branch
async function deleteBranch(branchId) {
    if (confirm('هل أنت متأكد من حذف هذا الفرع؟')) {
        try {
            await database.ref(`branches/${branchId}`).remove();
            showMessage('تم حذف الفرع بنجاح', 'success');
            loadBranches();
        } catch (error) {
            console.error('Error deleting branch:', error);
            showMessage('خطأ في حذف الفرع', 'error');
        }
    }
}

// Users management
async function loadUsers() {
    try {
        const usersRef = database.ref('users');
        const snapshot = await usersRef.once('value');
        const usersData = snapshot.val();
        
        const usersTableBody = document.getElementById('users-table-body');
        usersTableBody.innerHTML = '';
        
        if (usersData) {
            users = Object.entries(usersData).map(([key, value]) => ({id: key, ...value}));
            
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.name}</td>
                    <td>${user.email}</td>
                    <td>${user.phone}</td>
                    <td>${formatDate(user.createdAt)}</td>
                    <td>
                        <button onclick="deleteUser('${user.id}')" class="admin-btn danger">حذف</button>
                    </td>
                `;
                usersTableBody.appendChild(row);
            });
        } else {
            usersTableBody.innerHTML = '<tr><td colspan="5">لا توجد مستخدمين مسجلين</td></tr>';
        }
    } catch (error) {
        console.error('Error loading users:', error);
        showMessage('خطأ في تحميل المستخدمين', 'error');
    }
}

// Utility functions
function showLoading(show) {
    document.getElementById('loading-overlay').style.display = show ? 'flex' : 'none';
}

function showMessage(message, type) {
    const messageEl = document.getElementById('admin-message');
    messageEl.textContent = message;
    messageEl.className = `admin-message ${type}`;
    messageEl.style.display = 'block';
    
    setTimeout(() => {
        messageEl.style.display = 'none';
    }, 3000);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA');
}

// Messages management
async function loadMessages() {
    try {
        const messagesRef = database.ref('contactForms');
        const snapshot = await messagesRef.once('value');
        const messagesData = snapshot.val();

        const messagesList = document.getElementById('messages-list');
        messagesList.innerHTML = '';

        if (messagesData) {
            const messages = Object.entries(messagesData).map(([key, value]) => ({id: key, ...value}));

            messages.reverse().forEach(message => {
                const messageCard = document.createElement('div');
                messageCard.className = 'message-card';
                messageCard.innerHTML = `
                    <div class="message-header">
                        <h4>${message.name}</h4>
                        <span class="message-date">${formatDate(message.submittedAt)}</span>
                    </div>
                    <div class="message-content">
                        <p><strong>البريد الإلكتروني:</strong> ${message.email}</p>
                        <p><strong>رقم الهاتف:</strong> ${message.phone}</p>
                        <p><strong>الرسالة:</strong> ${message.message}</p>
                    </div>
                    <div class="message-actions">
                        <button onclick="markAsRead('${message.id}')" class="admin-btn">تم القراءة</button>
                        <button onclick="deleteMessage('${message.id}')" class="admin-btn danger">حذف</button>
                    </div>
                `;
                messagesList.appendChild(messageCard);
            });
        } else {
            messagesList.innerHTML = '<div class="no-data">لا توجد رسائل</div>';
        }
    } catch (error) {
        console.error('Error loading messages:', error);
        showMessage('خطأ في تحميل الرسائل', 'error');
    }
}

// Gallery management
async function loadGallery() {
    try {
        const galleryRef = database.ref('gallery');
        const snapshot = await galleryRef.once('value');
        const galleryData = snapshot.val();

        const galleryImages = document.getElementById('gallery-images');
        galleryImages.innerHTML = '';

        if (galleryData) {
            const images = Object.entries(galleryData).map(([key, value]) => ({id: key, ...value}));

            images.forEach(image => {
                const imageCard = document.createElement('div');
                imageCard.className = 'gallery-item';
                imageCard.innerHTML = `
                    <img src="${image.url}" alt="${image.alt}">
                    <div class="gallery-item-actions">
                        <button onclick="deleteImage('${image.id}')" class="admin-btn danger">حذف</button>
                    </div>
                `;
                galleryImages.appendChild(imageCard);
            });
        } else {
            galleryImages.innerHTML = '<div class="no-data">لا توجد صور في المعرض</div>';
        }
    } catch (error) {
        console.error('Error loading gallery:', error);
        showMessage('خطأ في تحميل المعرض', 'error');
    }
}

// Handle image upload
async function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
        showLoading(true);

        // Upload to Firebase Storage
        const storageRef = storage.ref(`gallery/${Date.now()}_${file.name}`);
        const uploadTask = await storageRef.put(file);
        const downloadURL = await uploadTask.ref.getDownloadURL();

        // Save to database
        const galleryRef = database.ref('gallery').push();
        await galleryRef.set({
            url: downloadURL,
            alt: file.name,
            uploadedAt: new Date().toISOString(),
            uploadedBy: currentUser.uid
        });

        showMessage('تم رفع الصورة بنجاح', 'success');
        loadGallery();
        showLoading(false);
    } catch (error) {
        console.error('Error uploading image:', error);
        showMessage('خطأ في رفع الصورة', 'error');
        showLoading(false);
    }
}

// Delete image
async function deleteImage(imageId) {
    if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
        try {
            await database.ref(`gallery/${imageId}`).remove();
            showMessage('تم حذف الصورة بنجاح', 'success');
            loadGallery();
        } catch (error) {
            console.error('Error deleting image:', error);
            showMessage('خطأ في حذف الصورة', 'error');
        }
    }
}

// Delete user
async function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        try {
            await database.ref(`users/${userId}`).remove();
            showMessage('تم حذف المستخدم بنجاح', 'success');
            loadUsers();
        } catch (error) {
            console.error('Error deleting user:', error);
            showMessage('خطأ في حذف المستخدم', 'error');
        }
    }
}

// Mark message as read
async function markAsRead(messageId) {
    try {
        await database.ref(`contactForms/${messageId}`).update({
            status: 'read',
            readAt: new Date().toISOString(),
            readBy: currentUser.uid
        });
        showMessage('تم تحديد الرسالة كمقروءة', 'success');
        loadMessages();
    } catch (error) {
        console.error('Error marking message as read:', error);
        showMessage('خطأ في تحديث حالة الرسالة', 'error');
    }
}

// Delete message
async function deleteMessage(messageId) {
    if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
        try {
            await database.ref(`contactForms/${messageId}`).remove();
            showMessage('تم حذف الرسالة بنجاح', 'success');
            loadMessages();
        } catch (error) {
            console.error('Error deleting message:', error);
            showMessage('خطأ في حذف الرسالة', 'error');
        }
    }
}

// Site settings
async function handleSiteSettings(event) {
    event.preventDefault();

    const maintenance = document.getElementById('site-maintenance').checked;
    const contactEmail = document.getElementById('contact-email').value;
    const contactPhone = document.getElementById('contact-phone').value;

    try {
        showLoading(true);

        await database.ref('siteSettings').set({
            maintenance: maintenance,
            contactEmail: contactEmail,
            contactPhone: contactPhone,
            updatedAt: new Date().toISOString(),
            updatedBy: currentUser.uid
        });

        showMessage('تم حفظ الإعدادات بنجاح', 'success');
        showLoading(false);
    } catch (error) {
        console.error('Error saving site settings:', error);
        showMessage('خطأ في حفظ الإعدادات', 'error');
        showLoading(false);
    }
}

// Edit branch
function editBranch(branchId) {
    const branch = branches.find(b => b.id === branchId);
    if (branch) {
        document.getElementById('branch-name').value = branch.name;
        document.getElementById('branch-address').value = branch.address;
        document.getElementById('branch-phone').value = branch.phone;
        showAddBranchForm();

        // Change form to edit mode
        const form = document.getElementById('branch-form');
        form.onsubmit = async (event) => {
            event.preventDefault();
            await updateBranch(branchId);
        };
    }
}

// Update branch
async function updateBranch(branchId) {
    const name = document.getElementById('branch-name').value;
    const address = document.getElementById('branch-address').value;
    const phone = document.getElementById('branch-phone').value;

    try {
        showLoading(true);

        await database.ref(`branches/${branchId}`).update({
            name: name,
            address: address,
            phone: phone,
            updatedAt: new Date().toISOString(),
            updatedBy: currentUser.uid
        });

        showMessage('تم تحديث الفرع بنجاح', 'success');
        hideAddBranchForm();
        loadBranches();
        showLoading(false);

        // Reset form to add mode
        document.getElementById('branch-form').onsubmit = handleAddBranch;
    } catch (error) {
        console.error('Error updating branch:', error);
        showMessage('خطأ في تحديث الفرع', 'error');
        showLoading(false);
    }
}

// Logout function
function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        localStorage.removeItem('user');
        window.location.href = 'login.html';
    }
}
