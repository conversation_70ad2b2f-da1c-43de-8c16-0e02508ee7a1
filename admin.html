<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة - AL-SALAMAT</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="admin.css">
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-storage-compat.js"></script>
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <nav class="admin-nav">
            <div class="admin-logo">AL-SALAMAT - لوحة الإدارة</div>
            <div class="admin-user-info">
                <span id="admin-username">مرحباً، المدير</span>
                <button onclick="logout()" class="logout-btn">تسجيل الخروج</button>
            </div>
        </nav>
    </header>

    <!-- Admin Sidebar -->
    <aside class="admin-sidebar">
        <ul class="admin-menu">
            <li><a href="#dashboard" onclick="showSection('dashboard')" class="active">لوحة التحكم</a></li>
            <li><a href="#content" onclick="showSection('content')">إدارة المحتوى</a></li>
            <li><a href="#branches" onclick="showSection('branches')">إدارة الفروع</a></li>
            <li><a href="#gallery" onclick="showSection('gallery')">إدارة المعرض</a></li>
            <li><a href="#users" onclick="showSection('users')">إدارة المستخدمين</a></li>
            <li><a href="#messages" onclick="showSection('messages')">رسائل التواصل</a></li>
            <li><a href="#settings" onclick="showSection('settings')">الإعدادات</a></li>
        </ul>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <!-- Dashboard Section -->
        <section id="dashboard" class="admin-section active">
            <h2>لوحة التحكم</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>إجمالي المستخدمين</h3>
                    <div class="stat-number" id="total-users">0</div>
                </div>
                <div class="stat-card">
                    <h3>رسائل جديدة</h3>
                    <div class="stat-number" id="new-messages">0</div>
                </div>
                <div class="stat-card">
                    <h3>عدد الفروع</h3>
                    <div class="stat-number" id="total-branches">4</div>
                </div>
                <div class="stat-card">
                    <h3>زيارات اليوم</h3>
                    <div class="stat-number" id="daily-visits">0</div>
                </div>
            </div>
            
            <div class="recent-activity">
                <h3>النشاط الأخير</h3>
                <div id="recent-activity-list" class="activity-list">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </section>

        <!-- Content Management Section -->
        <section id="content" class="admin-section">
            <h2>إدارة المحتوى</h2>
            
            <div class="content-form">
                <h3>تعديل معلومات الشركة</h3>
                <form id="company-info-form">
                    <div class="form-group">
                        <label for="company-title">عنوان الشركة</label>
                        <input type="text" id="company-title" value="AL-SALAMAT">
                    </div>
                    <div class="form-group">
                        <label for="company-subtitle">العنوان الفرعي</label>
                        <input type="text" id="company-subtitle" value="رائدة في زجاج السيارات">
                    </div>
                    <div class="form-group">
                        <label for="company-description">وصف الشركة (الصفحة الرئيسية)</label>
                        <textarea id="company-description" rows="3">نحن شركة رائدة في استيراد وتوزيع وتركيب وتصليح وقص زجاج السيارات، نخدم المجتمع المحلي ونسعى دائماً لأن تكون لنا لمستنا المميزة فيه.</textarea>
                    </div>
                    <button type="submit" class="admin-btn">حفظ التغييرات</button>
                </form>
            </div>

            <div class="content-form">
                <h3>إدارة قسم "من نحن"</h3>
                <form id="about-section-form">
                    <div class="form-group">
                        <label for="about-title">عنوان القسم</label>
                        <input type="text" id="about-title" value="من نحن">
                    </div>
                    <div class="form-group">
                        <label for="about-description">محتوى قسم "من نحن"</label>
                        <textarea id="about-description" rows="6">نحن شركة رائدة في استيراد وتوزيع وتركيب وتصليح وقص زجاج السيارات، نخدم المجتمع المحلي ونسعى دائماً لأن تكون لنا لمستنا المميزة فيه. نفتخر بخبرتنا الواسعة وجودة خدماتنا العالية، ونلتزم بتقديم أفضل الحلول لعملائنا الكرام بأحدث التقنيات والمعدات المتطورة.</textarea>
                    </div>
                    <button type="submit" class="admin-btn">حفظ قسم "من نحن"</button>
                </form>
            </div>




        </section>

        <!-- Branches Management Section -->
        <section id="branches" class="admin-section">
            <h2>إدارة الفروع</h2>
            
            <div class="section-header">
                <button onclick="showAddBranchForm()" class="admin-btn">إضافة فرع جديد</button>
            </div>
            
            <!-- Add Branch Form -->
            <div id="add-branch-form" class="form-modal" style="display: none;">
                <div class="modal-content">
                    <h3>إضافة فرع جديد</h3>
                    <form id="branch-form">
                        <div class="form-group">
                            <label for="branch-name">اسم الفرع</label>
                            <input type="text" id="branch-name" required>
                        </div>
                        <div class="form-group">
                            <label for="branch-address">العنوان</label>
                            <input type="text" id="branch-address" required>
                        </div>
                        <div class="form-group">
                            <label for="branch-phone">رقم الهاتف</label>
                            <input type="tel" id="branch-phone" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="admin-btn">حفظ</button>
                            <button type="button" onclick="hideAddBranchForm()" class="admin-btn secondary">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Branches List -->
            <div id="branches-list" class="branches-list">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </section>

        <!-- Gallery Management Section -->
        <section id="gallery" class="admin-section">
            <h2>إدارة المعرض</h2>
            
            <div class="upload-section">
                <h3>رفع صورة جديدة</h3>
                <div class="upload-area" onclick="document.getElementById('image-upload').click()">
                    <input type="file" id="image-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                    <div class="upload-text">اضغط لرفع صورة</div>
                </div>
            </div>
            
            <div id="gallery-images" class="gallery-grid">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </section>

        <!-- Users Management Section -->
        <section id="users" class="admin-section">
            <h2>إدارة المستخدمين</h2>
            
            <div class="users-table-container">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>رقم الهاتف</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Messages Section -->
        <section id="messages" class="admin-section">
            <h2>رسائل التواصل</h2>

            <div id="messages-list" class="messages-list">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="admin-section">
            <h2>الإعدادات</h2>

            <div class="content-form">
                <h3>إدارة قسم "اتصل بنا"</h3>
                <form id="contact-section-form">
                    <div class="form-group">
                        <label for="contact-title">عنوان قسم الاتصال</label>
                        <input type="text" id="contact-title" value="اتصل بنا">
                    </div>
                    <div class="form-group">
                        <label for="contact-info-title">عنوان معلومات التواصل</label>
                        <input type="text" id="contact-info-title" value="معلومات التواصل">
                    </div>
                    <div class="form-group">
                        <label for="contact-address">العنوان</label>
                        <input type="text" id="contact-address" value="المملكة العربية السعودية">
                    </div>
                    <div class="form-group">
                        <label for="contact-hours">ساعات العمل</label>
                        <input type="text" id="contact-hours" value="السبت - الخميس: 8:00 ص - 10:00 م">
                    </div>
                    <button type="submit" class="admin-btn">حفظ قسم الاتصال</button>
                </form>
            </div>

            <div class="settings-form">
                <h3>إعدادات الموقع</h3>
                <form id="site-settings-form">
                    <div class="form-group">
                        <label for="site-maintenance">وضع الصيانة</label>
                        <input type="checkbox" id="site-maintenance">
                        <span>تفعيل وضع الصيانة</span>
                    </div>
                    <div class="form-group">
                        <label for="contact-email">بريد التواصل</label>
                        <input type="email" id="contact-email" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="contact-phone">رقم التواصل الرئيسي</label>
                        <input type="tel" id="contact-phone" value="+966501234567">
                    </div>
                    <button type="submit" class="admin-btn">حفظ الإعدادات</button>
                </form>
            </div>
        </section>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    <!-- Success/Error Messages -->
    <div id="admin-message" class="admin-message" style="display: none;"></div>

    <script src="admin.js"></script>
</body>
</html>
