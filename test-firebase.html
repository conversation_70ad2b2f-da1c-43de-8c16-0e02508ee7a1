<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Firebase - AL-SALAMAT</title>
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-storage-compat.js"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار اتصال Firebase</h1>
        
        <div id="results"></div>
        
        <button onclick="testFirebaseConnection()">اختبار الاتصال</button>
        <button onclick="testDatabaseRead()">اختبار قراءة البيانات</button>
        <button onclick="testDatabaseWrite()">اختبار كتابة البيانات</button>
        <button onclick="clearResults()">مسح النتائج</button>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        let database, storage;
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testFirebaseConnection() {
            try {
                addResult('بدء اختبار الاتصال...', 'info');
                
                // Check if Firebase is loaded
                if (typeof firebase === 'undefined') {
                    addResult('❌ Firebase غير محمل', 'error');
                    return;
                }
                addResult('✅ Firebase محمل بنجاح', 'success');
                
                // Initialize Firebase
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                    addResult('✅ تم تهيئة Firebase', 'success');
                } else {
                    addResult('✅ Firebase مهيأ مسبقاً', 'success');
                }
                
                // Initialize services
                database = firebase.database();
                storage = firebase.storage();
                
                addResult('✅ تم تهيئة Database', 'success');
                addResult('✅ تم تهيئة Storage', 'success');
                
                // Test database connection
                const testRef = database.ref('.info/connected');
                testRef.on('value', (snapshot) => {
                    if (snapshot.val() === true) {
                        addResult('✅ متصل بقاعدة البيانات', 'success');
                    } else {
                        addResult('❌ غير متصل بقاعدة البيانات', 'error');
                    }
                });
                
            } catch (error) {
                addResult(`❌ خطأ في الاتصال: ${error.message}`, 'error');
                console.error('Firebase connection error:', error);
            }
        }

        async function testDatabaseRead() {
            try {
                addResult('اختبار قراءة البيانات...', 'info');
                
                if (!database) {
                    addResult('❌ قاعدة البيانات غير مهيأة', 'error');
                    return;
                }
                
                // Test reading users
                const usersSnapshot = await database.ref('users').once('value');
                const usersData = usersSnapshot.val();
                const usersCount = usersData ? Object.keys(usersData).length : 0;
                addResult(`✅ تم قراءة المستخدمين: ${usersCount} مستخدم`, 'success');
                
                // Test reading messages
                const messagesSnapshot = await database.ref('contactForms').once('value');
                const messagesData = messagesSnapshot.val();
                const messagesCount = messagesData ? Object.keys(messagesData).length : 0;
                addResult(`✅ تم قراءة الرسائل: ${messagesCount} رسالة`, 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في قراءة البيانات: ${error.message}`, 'error');
                console.error('Database read error:', error);
            }
        }

        async function testDatabaseWrite() {
            try {
                addResult('اختبار كتابة البيانات...', 'info');
                
                if (!database) {
                    addResult('❌ قاعدة البيانات غير مهيأة', 'error');
                    return;
                }
                
                // Test writing data
                const testData = {
                    message: 'اختبار الكتابة',
                    timestamp: new Date().toISOString(),
                    test: true
                };
                
                await database.ref('test').set(testData);
                addResult('✅ تم كتابة البيانات بنجاح', 'success');
                
                // Test reading the written data
                const snapshot = await database.ref('test').once('value');
                const data = snapshot.val();
                
                if (data && data.message === testData.message) {
                    addResult('✅ تم التحقق من البيانات المكتوبة', 'success');
                } else {
                    addResult('❌ فشل في التحقق من البيانات المكتوبة', 'error');
                }
                
                // Clean up test data
                await database.ref('test').remove();
                addResult('✅ تم حذف بيانات الاختبار', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في كتابة البيانات: ${error.message}`, 'error');
                console.error('Database write error:', error);
            }
        }

        // Auto-run connection test on page load
        window.addEventListener('load', () => {
            setTimeout(testFirebaseConnection, 1000);
        });
    </script>
</body>
</html>
