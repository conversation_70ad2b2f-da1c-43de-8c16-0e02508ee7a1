# AL-SALAMAT - موقع شركة زجاج السيارات

موقع ويب لشركة السلامة لزجاج السيارات مع نظام تسجيل الدخول باستخدام Firebase.

## المميزات

### الميزات الأساسية
- 🎨 تصميم عصري ومتجاوب
- 🔐 نظام تسجيل دخول وإنشاء حساب باستخدام Firebase
- 📱 شريط جانبي للموبايل
- 🖼️ معرض صور للخدمات
- 🏢 عرض الفروع مع معلومات الاتصال
- 📧 نموذج اتصال تفاعلي

### لوحة الإدارة
- 👨‍💼 لوحة إدارة شاملة للمديرين
- 📊 إحصائيات ولوحة تحكم
- 🗂️ إدارة المحتوى والفروع
- 👥 إدارة المستخدمين والرسائل
- 🖼️ إدارة معرض الصور
- ⚙️ إعدادات الموقع

### الميزات الديناميكية الجديدة ⭐
- 🔄 **تحديث في الوقت الفعلي**: المحتوى يتحدث فوراً من لوحة الإدارة
- 🎯 **تفاعل مباشر**: تغييرات فورية بدون إعادة تحميل الصفحة
- ✨ **رسوم متحركة**: تأثيرات بصرية عند التحديث
- 📡 **Firebase Realtime**: اتصال مباشر مع قاعدة البيانات
- 🔔 **مؤشر التحديث**: إشعارات بصرية للتحديثات الجديدة

## التقنيات المستخدمة

- HTML5
- CSS3
- JavaScript (ES6+)
- Firebase Authentication
- Firebase Firestore

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
npm install
```

### 2. تشغيل الموقع

```bash
npm start
```

أو

```bash
npm run dev
```

سيتم فتح الموقع على: `http://localhost:3000`

## إعداد Firebase

الموقع مُعد مسبقاً للعمل مع Firebase باستخدام المعلومات التالية:

- **Project ID:** al-salamat
- **Auth Domain:** al-salamat.firebaseapp.com
- **Database URL:** https://al-salamat-default-rtdb.firebaseio.com

## الملفات الرئيسية

- `index.html` - الصفحة الرئيسية
- `login.html` - صفحة تسجيل الدخول وإنشاء الحساب
- `admin.html` - لوحة الإدارة
- `styles.css` - ملف التصميم الرئيسي
- `admin.css` - ملف تصميم لوحة الإدارة
- `admin.js` - وظائف لوحة الإدارة
- `dynamic-content.js` - إدارة المحتوى الديناميكي ⭐
- `firebase-config.js` - إعدادات Firebase
- `package.json` - معلومات المشروع والمتطلبات

## النظام الديناميكي الجديد ⭐

### كيفية العمل
1. **المدير يعدل المحتوى** في لوحة الإدارة
2. **التغييرات تُحفظ** في Firebase فوراً
3. **الصفحة الرئيسية تتلقى التحديث** تلقائياً
4. **المحتوى يتحدث** بدون إعادة تحميل الصفحة
5. **مؤشر بصري** يظهر للمستخدمين

### العناصر القابلة للتحديث
- ✅ **عنوان الشركة** والعنوان الفرعي
- ✅ **وصف الشركة** في قسم "من نحن"
- ✅ **قائمة الفروع** (إضافة/تعديل/حذف)
- ✅ **صور المعرض** (رفع/حذف)
- ✅ **معلومات التواصل** (البريد والهاتف)

### المميزات التقنية
- 🔥 **Firebase Realtime Database** للتحديث الفوري
- 🎨 **رسوم متحركة** عند التحديث
- 🛡️ **أمان متقدم** ضد XSS
- 📱 **استجابة كاملة** لجميع الأجهزة
- ⚡ **أداء محسن** مع تحميل تدريجي

## لوحة الإدارة

### الوصول للوحة الإدارة
- يجب تسجيل الدخول أولاً
- المستخدمون المخولون فقط يمكنهم الوصول
- الرابط يظهر في قائمة المستخدم للمديرين

### مميزات لوحة الإدارة
- **لوحة التحكم**: إحصائيات شاملة ونشاط حديث
- **إدارة المحتوى**: تعديل معلومات الشركة والوصف
- **إدارة الفروع**: إضافة وتعديل وحذف الفروع
- **إدارة المعرض**: رفع وحذف صور المعرض
- **إدارة المستخدمين**: عرض وحذف المستخدمين المسجلين
- **رسائل التواصل**: عرض وإدارة رسائل العملاء
- **الإعدادات**: إعدادات الموقع العامة

### صلاحيات الإدارة

#### المدير الرئيسي
- **البريد الإلكتروني**: `<EMAIL>`
- **الصلاحيات**: جميع الصلاحيات (إدارة كاملة)
- **التسجيل**: عند إنشاء حساب بهذا البريد، يحصل تلقائياً على صلاحيات المدير

#### إضافة مديرين جدد
لإعطاء صلاحيات إدارة لمستخدم جديد:
1. أضف البريد الإلكتروني في قائمة `adminEmails` في الملفات التالية:
   - `index.html` (السطر 232)
   - `admin.js` (السطر 49)
   - `login.html` (السطر 400)
   - `firebase-config.js` (السطر 39)

#### الصلاحيات المتاحة
- **إدارة المحتوى**: تعديل معلومات الشركة
- **إدارة الفروع**: إضافة وتعديل وحذف الفروع
- **إدارة المستخدمين**: عرض وحذف المستخدمين
- **إدارة الرسائل**: عرض وإدارة رسائل العملاء
- **إدارة المعرض**: رفع وحذف الصور
- **إدارة الإعدادات**: تعديل إعدادات الموقع

## الوظائف

### تسجيل الدخول
- تسجيل دخول بالبريد الإلكتروني وكلمة المرور
- رسائل خطأ باللغة العربية
- حفظ بيانات المستخدم في localStorage

### إنشاء حساب
- إنشاء حساب جديد بالبيانات الشخصية
- حفظ البيانات في Firestore
- التحقق من تطابق كلمة المرور

### الأمان
- التحقق من صحة البيانات
- رسائل خطأ واضحة
- حماية من الهجمات الشائعة

## المتصفحات المدعومة

- Chrome (الأحدث)
- Firefox (الأحدث)
- Safari (الأحدث)
- Edge (الأحدث)

## الدعم

للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.
